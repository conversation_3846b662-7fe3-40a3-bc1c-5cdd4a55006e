<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" @ok="handleSubmit" width="70%">
    <a-form
        ref="formRef"
        :model="procurementModel"
        @submit="handleSubmit"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
        :rules="validatorRules">

      <a-row class="form-row" :gutter="16">
        <!-- 申购类型 -->
        <a-col :span="20">
          <a-form-item label="申购类型" name="buyType">
            <a-select v-model:value="procurementModel.buyType" placeholder="请选择申购类型">
              <a-select-option value="未书面审批过">未书面审批过</a-select-option>
              <a-select-option value="已书面审批同意">已书面审批同意</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 申购资产名称 -->
        <a-col :span="20">
          <a-form-item label="申购资产名称" name="assetName">
            <a-input v-model:value="procurementModel.assetName" placeholder="请输入申购资产名称" />
          </a-form-item>
        </a-col>

        <!-- 规格 -->
        <a-col :span="20">
          <a-form-item label="规格" name="specification">
            <a-input v-model:value="procurementModel.specification" placeholder="请输入规格" />
          </a-form-item>
        </a-col>

        <!-- 数量 -->
        <a-col :span="20">
          <a-form-item label="数量" name="quantity">
            <a-input-number
              v-model:value="procurementModel.quantity"
              placeholder="请输入数量"
              :min="1"
              style="width: 100%"
            />
          </a-form-item>
        </a-col>

        <!-- 单位 -->
        <a-col :span="20">
          <a-form-item label="单位" name="unit">
            <a-input v-model:value="procurementModel.unit" placeholder="请输入单位" />
          </a-form-item>
        </a-col>

        <!-- 预估单价 -->
        <a-col :span="20">
          <a-form-item label="预估单价" name="estimatedUnitPrice">
            <a-input-number
              v-model:value="procurementModel.estimatedUnitPrice"
              placeholder="请输入预估单价"
              :min="0"
              :precision="2"
              style="width: 100%"
              @change="calculateTotalAmount"
            />
          </a-form-item>
        </a-col>

        <!-- 预估金额 -->
        <a-col :span="20">
          <a-form-item label="预估金额" name="estimatedTotalAmount">
            <a-input-number
              v-model:value="procurementModel.estimatedTotalAmount"
              placeholder="预估金额"
              :min="0"
              :precision="2"
              style="width: 100%"
              disabled
            />
          </a-form-item>
        </a-col>

        <!-- 请购原因 -->
        <a-col :span="20">
          <a-form-item label="请购原因" name="procurementReason">
            <a-textarea
              v-model:value="procurementModel.procurementReason"
              placeholder="请输入请购原因"
              :rows="3"
              show-count
              :maxlength="500"
            />
          </a-form-item>
        </a-col>

        <!-- 技术指标 -->
        <a-col :span="20">
          <a-form-item label="技术指标" name="technicalIndicators">
            <a-textarea
              v-model:value="procurementModel.technicalIndicators"
              placeholder="请输入技术指标"
              :rows="3"
              show-count
              :maxlength="1000"
            />
          </a-form-item>
        </a-col>

        <!-- 功能需求 -->
        <a-col :span="20">
          <a-form-item label="功能需求" name="functionalRequirements">
            <a-textarea
              v-model:value="procurementModel.functionalRequirements"
              placeholder="请输入功能需求（选填）"
              :rows="3"
              show-count
              :maxlength="1000"
            />
          </a-form-item>
        </a-col>

     

        <!-- 资产使用目的 -->
        <a-col :span="20">
          <a-form-item label="资产使用目的" name="usePurpose">
            <a-select v-model:value="procurementModel.usePurpose" placeholder="请选择资产使用目的">
              <a-select-option value="部门日常需求">部门日常需求</a-select-option>
              <a-select-option value="扩大生产规模">扩大生产规模</a-select-option>
              <a-select-option value="节约开支">节约开支</a-select-option>
              <a-select-option value="维修/更换">维修/更换</a-select-option>
              <a-select-option value="开发新产品/新市场">开发新产品/新市场</a-select-option>
              <a-select-option value="法律/质量">法律/质量</a-select-option>
              <a-select-option value="其它">其它</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 资金支出属性 -->
        <a-col :span="20">
          <a-form-item label="资金支出属性" name="fundingAttribute">
            <a-select v-model:value="procurementModel.fundingAttribute" placeholder="请选择资金支出属性">
              <a-select-option value="机器/设备">机器/设备</a-select-option>
              <a-select-option value="仪器">仪器</a-select-option>
              <a-select-option value="备件">备件</a-select-option>
              <a-select-option value="工具">工具</a-select-option>
              <a-select-option value="交通">交通</a-select-option>
              <a-select-option value="办公设备">办公设备</a-select-option>
              <a-select-option value="信息处理">信息处理</a-select-option>
            </a-select>
          </a-form-item>
        </a-col>

        <!-- 资产要求到位时间 -->
        <a-col :span="20">
          <a-form-item label="资产要求到位时间" name="requiredDeliveryDate">
            <a-date-picker
              v-model:value="procurementModel.requiredDeliveryDate"
              placeholder="请选择资产要求到位时间"
              style="width: 100%"
              format="YYYY-MM-DD"
              valueFormat="YYYY-MM-DD"
            />
          </a-form-item>
        </a-col>

      </a-row>
    </a-form>
  </BasicModal>
</template>

<script lang="ts" setup>
import { reactive, ref, computed, unref, watch } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { message } from 'ant-design-vue';
import type { FormInstance } from 'ant-design-vue';
import { saveProcurementForm } from '../instrumentManagement.api';

// Emits声明
const emit = defineEmits(['register', 'success']);
const formRef = ref<FormInstance>();
const isUpdate = ref(false);
const isFooter = ref(true);

// 表单布局配置
const labelCol = reactive({
  xs: { span: 24 },
  sm: { span: 6 },
});
const wrapperCol = reactive({
  xs: { span: 24 },
  sm: { span: 18 },
});

// 申购表单数据模型
const procurementModel = reactive({
  id: null as string | null,
  buyType: null as string | null,        // 申购类型*
  assetName: null as string | null,              // 申购资产名称*
  specification: null as string | null,          // 规格*
  quantity: null as number | null,               // 数量*
  unit: null as string | null,                   // 单位*
  estimatedUnitPrice: null as number | null,     // 预估单价*
  estimatedTotalAmount: null as number | null,   // 预估金额*
  procurementReason: null as string | null,      // 请购原因*
  technicalIndicators: null as string | null,    // 技术指标
  functionalRequirements: null as string | null, // 功能需求
  usePurpose: null as string | null,      // 资产使用目的*
  fundingAttribute: null as string | null,       // 资金支出属性*
  requiredDeliveryDate: null as string | null,   // 资产要求到位时间
  departmentType: null as string | null,        // 部门类型*
  propertyDepartment: null as string | null,     // 资产管理部门*
  instrumentId: null as string | null,          //仪器id
});

// 表单验证规则
const validatorRules = {
  buyType: [{ required: true, message: '请选择申购类型！' }],
  assetName: [{ required: true, message: '请输入申购资产名称！' }],
  specification: [{ required: true, message: '请输入规格！' }],
  quantity: [{ required: true, message: '请输入数量！' }],
  unit: [{ required: true, message: '请选择单位！' }],
  estimatedUnitPrice: [{ required: true, message: '请输入预估单价！' }],
  estimatedTotalAmount: [{ required: true, message: '预估金额不能为空！' }],
  procurementReason: [{ required: true, message: '请输入请购原因！' }],
  usePurpose: [{ required: true, message: '请输入资产使用目的！' }],
  fundingAttribute: [{ required: true, message: '请选择资金支出属性！' }],

};

// 计算预估金额
const calculateTotalAmount = () => {
  if (procurementModel.quantity && procurementModel.estimatedUnitPrice) {
    procurementModel.estimatedTotalAmount = Number((procurementModel.quantity * procurementModel.estimatedUnitPrice).toFixed(2));
  } else {
    procurementModel.estimatedTotalAmount = null;
  }
};

// 监听数量变化，自动计算金额
watch(() => procurementModel.quantity, () => {
  calculateTotalAmount();
});

//表单赋值
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  console.log("🚀 ~ data:", data)
  //重置表单
  reset();
  setModalProps({ confirmLoading: false, showCancelBtn: data?.showFooter, showOkBtn: data?.showFooter });
  isUpdate.value = !!data?.isUpdate;
  isFooter.value = !!data?.showFooter;
  if (unref(isUpdate)) {
    //表单赋值
    if (data.record.propertyName) {
      // 编辑采购记录模式 - 直接映射采购记录字段
      procurementModel.id = data.record.id;
      procurementModel.buyType = data.record.buyType;
      procurementModel.assetName = data.record.propertyName;
      procurementModel.specification = data.record.spec;
      procurementModel.quantity = data.record.buyNumber;
      procurementModel.unit = data.record.unit;
      procurementModel.estimatedUnitPrice = data.record.singlePrice;
      procurementModel.estimatedTotalAmount = data.record.estimatePrice;
      procurementModel.departmentType = data.record.departmentType;
      procurementModel.propertyDepartment = data.record.propertyDepartment;
      procurementModel.instrumentId = data.record.instrumentId;
    } else {
      // 新增采购记录模式 - 从仪器信息映射
      Object.assign(procurementModel, data.record);
      procurementModel.assetName = data.record.instrumentName;
      procurementModel.specification = data.record.instrumentModel;
      procurementModel.estimatedUnitPrice = data.record.price;
      procurementModel.departmentType='其他'
      procurementModel.propertyDepartment='质量管理中心'
      procurementModel.instrumentId =data.record.id
    }
  }
  console.log('🚀 ~ procurementModal ~ procurementModel:', procurementModel);
});

//设置标题
const title = computed(() => (!unref(isUpdate) ? '新增申购' : '编辑申购'));

//表单提交事件
function handleSubmit() {
  if (!formRef.value) return;

  formRef.value
    .validate()
    .then(async () => {
      try {
        setModalProps({ confirmLoading: true });

        // 提交申购表单数据
        await saveProcurementForm(procurementModel, isUpdate.value);
        console.log('提交申购数据:', procurementModel);
        //关闭弹窗
        closeModal();
        //刷新列表
        emit('success', { isUpdate: isUpdate.value, procurementModel });
      } finally {
        setModalProps({ confirmLoading: false });
      }
    })
    .catch((error: any) => {
      console.log('表单验证失败:', error);
    });
}

// 重置表单
function reset() {
  procurementModel.id = null;
  procurementModel.buyType = null;
  procurementModel.assetName = null;
  procurementModel.specification = null;
  procurementModel.quantity = null;
  procurementModel.unit = null;
  procurementModel.estimatedUnitPrice = null;
  procurementModel.estimatedTotalAmount = null;
  procurementModel.procurementReason = null;
  procurementModel.technicalIndicators = null;
  procurementModel.functionalRequirements = null;
  procurementModel.usePurpose = null;
  procurementModel.fundingAttribute = null;
  procurementModel.requiredDeliveryDate = null;
  procurementModel.departmentType = null;
  procurementModel.propertyDepartment = null;
  procurementModel.instrumentId = null;
}
</script>

<style lang="less" scoped>
.form-row {
  margin-bottom: 16px;
}

.fontColor {
  color: black;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-input-number) {
  width: 100%;
}

:deep(.ant-select) {
  width: 100%;
}
</style>